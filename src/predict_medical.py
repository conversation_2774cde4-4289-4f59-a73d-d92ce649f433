#!/usr/bin/env python3
"""
医学影像MR序列分类预测脚本

使用训练好的模型对单个nii.gz文件进行MR序列分类预测。
支持与训练时相同的3通道切片堆叠方式。
"""

import os
import sys
import torch
import numpy as np
import SimpleITK as sitk
from PIL import Image
from torchvision import transforms
import torch.nn.functional as F

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 移除了不需要的导入，因为尺寸处理已经在数据加载器中完成


class MedicalImagePredictor:
    """医学影像MR序列分类预测器"""
    
    def __init__(self, model_path, slice_mode='middle', axis='z', device=None):
        """
        初始化预测器
        
        Args:
            model_path (str): 训练好的模型路径
            slice_mode (str): 切片模式，'middle' 或 'multi'
            axis (str): 当slice_mode='multi'时指定轴向
            device: 计算设备
        """
        self.slice_mode = slice_mode
        self.axis = axis
        self.device = device or torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 加载模型
        self.model, self.class_names = self._load_model(model_path)
        
        # 定义预处理变换
        # 注意：不需要PadToSquare，因为在_stack_slices_to_rgb中已经处理了尺寸统一
        self.transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    def _load_model(self, model_path):
        """加载训练好的模型"""
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型文件不存在: {model_path}")
        
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # 获取类别名称
        class_names = checkpoint.get('class_names', ['t1', 't2', 'flair', 'dwi'])
        num_classes = len(class_names)
        
        # 创建模型（这里假设使用ResNet50，您可以根据需要修改）
        from torchvision import models
        import torch.nn as nn
        
        model = models.resnet50(pretrained=False)
        model.fc = nn.Linear(model.fc.in_features, num_classes)
        
        # 加载模型权重
        model.load_state_dict(checkpoint['model_state_dict'])
        model = model.to(self.device)
        model.eval()
        
        print(f"✓ 模型加载成功")
        print(f"✓ 类别: {class_names}")
        print(f"✓ 设备: {self.device}")
        
        return model, class_names
    
    def _extract_slices_from_nii(self, nii_path):
        """从nii.gz文件提取3通道切片"""
        try:
            # 读取医学影像
            image = sitk.ReadImage(nii_path)
            arr = sitk.GetArrayFromImage(image)
            
            if self.slice_mode == 'middle':
                return self._extract_middle_slices(arr)
            elif self.slice_mode == 'multi':
                return self._extract_multi_slices(arr)
            else:
                raise ValueError(f"不支持的切片模式: {self.slice_mode}")
                
        except Exception as e:
            print(f"读取文件 {nii_path} 时出错: {e}")
            # 返回默认的黑色RGB图像
            return Image.new('RGB', (224, 224), (0, 0, 0))
    
    def _extract_middle_slices(self, arr):
        """提取xyz面的中间切片并堆叠成3通道"""
        depth, height, width = arr.shape
        
        # Z轴中间切片 (轴向面)
        z_middle = depth // 2
        axial_slice = arr[z_middle, :, :]
        
        # Y轴中间切片 (冠状面)
        y_middle = height // 2
        coronal_slice = arr[:, y_middle, :]
        
        # X轴中间切片 (矢状面)
        x_middle = width // 2
        sagittal_slice = arr[:, :, x_middle]
        
        # 将三个切片堆叠成3通道图像
        return self._stack_slices_to_rgb(axial_slice, coronal_slice, sagittal_slice)
    
    def _extract_multi_slices(self, arr):
        """提取指定轴的25%、50%、75%位置切片并堆叠成3通道"""
        depth, height, width = arr.shape
        
        if self.axis == 'z':
            positions = [int(depth * 0.25), int(depth * 0.5), int(depth * 0.75)]
            slice1 = arr[min(positions[0], depth - 1), :, :]
            slice2 = arr[min(positions[1], depth - 1), :, :]
            slice3 = arr[min(positions[2], depth - 1), :, :]
                
        elif self.axis == 'y':
            positions = [int(height * 0.25), int(height * 0.5), int(height * 0.75)]
            slice1 = arr[:, min(positions[0], height - 1), :]
            slice2 = arr[:, min(positions[1], height - 1), :]
            slice3 = arr[:, min(positions[2], height - 1), :]
                
        elif self.axis == 'x':
            positions = [int(width * 0.25), int(width * 0.5), int(width * 0.75)]
            slice1 = arr[:, :, min(positions[0], width - 1)]
            slice2 = arr[:, :, min(positions[1], width - 1)]
            slice3 = arr[:, :, min(positions[2], width - 1)]
        else:
            raise ValueError(f"不支持的轴向: {self.axis}")
        
        # 将三个切片堆叠成3通道图像
        return self._stack_slices_to_rgb(slice1, slice2, slice3)
    
    def _normalize_slice(self, arr):
        """标准化单个切片到0-255范围"""
        arr_min, arr_max = arr.min(), arr.max()
        if arr_max > arr_min:
            return ((arr - arr_min) / (arr_max - arr_min) * 255).astype(np.uint8)
        else:
            return np.zeros_like(arr, dtype=np.uint8)
    
    def _stack_slices_to_rgb(self, slice1, slice2, slice3, target_size=224):
        """将三个切片堆叠成RGB图像，并调整到目标尺寸"""
        # 确保所有切片具有相同的尺寸
        target_shape = slice1.shape

        # 如果切片尺寸不同，需要调整到相同尺寸
        if slice2.shape != target_shape:
            slice2 = self._resize_slice(slice2, target_shape)
        if slice3.shape != target_shape:
            slice3 = self._resize_slice(slice3, target_shape)

        # 标准化每个切片
        slice1_norm = self._normalize_slice(slice1)
        slice2_norm = self._normalize_slice(slice2)
        slice3_norm = self._normalize_slice(slice3)

        # 堆叠成RGB图像
        rgb_array = np.stack([slice1_norm, slice2_norm, slice3_norm], axis=-1)

        # 转换为PIL图像
        pil_image = Image.fromarray(rgb_array, mode='RGB')

        # 调整到目标尺寸，保持纵横比
        pil_image = self._resize_with_padding(pil_image, target_size)

        return pil_image

    def _resize_with_padding(self, image, target_size):
        """调整图像尺寸并保持纵横比，使用填充"""
        w, h = image.size

        # 计算缩放比例
        scale = min(target_size / w, target_size / h)
        new_w, new_h = int(w * scale), int(h * scale)

        # 缩放图像
        resized_image = image.resize((new_w, new_h), Image.LANCZOS)

        # 创建目标尺寸的背景图像
        new_image = Image.new('RGB', (target_size, target_size), (0, 0, 0))

        # 计算粘贴位置（居中）
        paste_x = (target_size - new_w) // 2
        paste_y = (target_size - new_h) // 2

        # 粘贴缩放后的图像
        new_image.paste(resized_image, (paste_x, paste_y))

        return new_image
    
    def _resize_slice(self, slice_arr, target_shape):
        """调整切片尺寸"""
        try:
            from scipy import ndimage
            zoom_factors = [target_shape[i] / slice_arr.shape[i] for i in range(len(target_shape))]
            return ndimage.zoom(slice_arr, zoom_factors, order=1)
        except ImportError:
            # 如果没有scipy，使用简单的重复/截断方法
            return np.resize(slice_arr, target_shape)
    
    def predict(self, nii_path, return_probabilities=False):
        """
        对单个nii.gz文件进行预测
        
        Args:
            nii_path (str): nii.gz文件路径
            return_probabilities (bool): 是否返回概率分布
            
        Returns:
            dict: 预测结果
        """
        # 提取3通道切片
        image = self._extract_slices_from_nii(nii_path)
        
        # 预处理
        input_tensor = self.transform(image).unsqueeze(0).to(self.device)
        
        # 预测
        with torch.no_grad():
            outputs = self.model(input_tensor)
            probabilities = F.softmax(outputs, dim=1)
            predicted_class_idx = torch.argmax(probabilities, dim=1).item()
            confidence = probabilities[0][predicted_class_idx].item()
        
        # 构建结果
        result = {
            'file_path': nii_path,
            'predicted_class': self.class_names[predicted_class_idx],
            'predicted_class_idx': predicted_class_idx,
            'confidence': confidence,
            'slice_mode': self.slice_mode,
            'axis': self.axis if self.slice_mode == 'multi' else None
        }
        
        if return_probabilities:
            result['probabilities'] = {
                self.class_names[i]: probabilities[0][i].item() 
                for i in range(len(self.class_names))
            }
        
        return result
    
    def predict_batch(self, nii_paths, return_probabilities=False):
        """
        批量预测多个nii.gz文件
        
        Args:
            nii_paths (list): nii.gz文件路径列表
            return_probabilities (bool): 是否返回概率分布
            
        Returns:
            list: 预测结果列表
        """
        results = []
        
        for nii_path in nii_paths:
            try:
                result = self.predict(nii_path, return_probabilities)
                results.append(result)
                print(f"✓ {os.path.basename(nii_path)}: {result['predicted_class']} "
                      f"(置信度: {result['confidence']:.3f})")
            except Exception as e:
                print(f"✗ {os.path.basename(nii_path)}: 预测失败 - {e}")
                results.append({
                    'file_path': nii_path,
                    'error': str(e)
                })
        
        return results


def main():
    """主函数 - 命令行使用示例"""
    import argparse
    
    parser = argparse.ArgumentParser(description='医学影像MR序列分类预测')
    parser.add_argument('--model', required=True, help='模型文件路径')
    parser.add_argument('--input', required=True, help='输入nii.gz文件路径')
    parser.add_argument('--slice_mode', default='middle', choices=['middle', 'multi'], 
                       help='切片模式')
    parser.add_argument('--axis', default='z', choices=['x', 'y', 'z'], 
                       help='multi模式下的轴向')
    parser.add_argument('--probabilities', action='store_true', 
                       help='显示概率分布')
    
    args = parser.parse_args()
    
    # 创建预测器
    predictor = MedicalImagePredictor(
        model_path=args.model,
        slice_mode=args.slice_mode,
        axis=args.axis
    )
    
    # 进行预测
    result = predictor.predict(args.input, return_probabilities=args.probabilities)
    
    # 显示结果
    print("\n" + "="*50)
    print("预测结果")
    print("="*50)
    print(f"文件: {os.path.basename(args.input)}")
    print(f"预测序列: {result['predicted_class']}")
    print(f"置信度: {result['confidence']:.3f}")
    print(f"切片模式: {result['slice_mode']}")
    if result['axis']:
        print(f"轴向: {result['axis']}")
    
    if args.probabilities and 'probabilities' in result:
        print("\n概率分布:")
        for class_name, prob in result['probabilities'].items():
            print(f"  {class_name}: {prob:.3f}")


if __name__ == "__main__":
    main()
