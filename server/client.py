import time
import requests
import numpy as np
import  SimpleITK as sitk
import io
from PIL import Image
import argparse

API_URL = "http://127.0.0.1:8080/predict"

def image2d_to_obj(arr, resize_rect=None):
    rgb_image = Image.fromarray(arr)
    if resize_rect:
        rgb_image = rgb_image.resize(resize_rect, Image.LANCZOS)
    return rgb_image


def ct_arr_to_gray(arr, window_width=400, window_center=40):
    return np.uint8(np.clip(255 * ((arr - window_center) / (window_width) + 1 / 2), 0, 255))


def cut_nii_to_image_obj(nii_path):
    image = sitk.ReadImage(nii_path)
    arr = sitk.GetArrayFromImage(image)
    depth, height, width = arr.shape
    spacing_width, spacing_height, spacing_depth = image.GetSpacing()
    arr = arr[::-1, :, :]
    arr1 = arr[:, int(height / 2), :]
    arr2 = arr[:, :, int(width / 2)]

    resize_rect = (width, int(depth * spacing_depth / spacing_width))
    cor_img_obj = image2d_to_obj(ct_arr_to_gray(arr1), resize_rect=resize_rect)

    resize_rect = (height, int(depth * spacing_depth / spacing_height))
    sag_img_obj = image2d_to_obj(ct_arr_to_gray(arr2), resize_rect=resize_rect)
    return cor_img_obj, sag_img_obj


def predict_nii(nii_pth):
    # 2. 构建 multipart/form-data 请求
    print("\n--- 发送请求 (multipart/form-data) ---")
    try:

        cor_img_obj, sag_img_obj = cut_nii_to_image_obj(nii_pth)
        cor_array = np.array(cor_img_obj)
        sag_array = np.array(sag_img_obj)

        # 将NumPy数组序列化到内存
        cor_buffer = io.BytesIO()
        np.save(cor_buffer, cor_array)
        cor_buffer.seek(0)

        sag_buffer = io.BytesIO()
        np.save(sag_buffer, sag_array)
        sag_buffer.seek(0)

        files = {
            'cor_array_file': ('cor_slice.npy', cor_buffer.getvalue(), 'application/x-numpy'),
            'sag_array_file': ('sag_slice.npy', sag_buffer.getvalue(), 'application/x-numpy'),
        }
        start_time = time.time()
        response = requests.post(API_URL, files=files, timeout=30)  # data=data
        response.raise_for_status()
        result = response.json()
        print(f"\n✅ 请求成功！, 耗时：{(time.time() -  start_time):.6f}秒")
        print("服务器返回结果:")
        print(result)
    except requests.exceptions.RequestException as e:
        print(f"\n❌ 请求失败: {e}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='nii predict demo')
    parser.add_argument('-f',required=True,  help='nii file path')
    args = parser.parse_args()
    predict_nii(args.f)