import torch
from torchvision import models, transforms
import os
import torch.nn as nn
from PIL import Image
import logging

logger = logging.getLogger(__name__)

BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath('__file__')))

from config.config import Config
from src.utils import PadToSquare

class ResNet50Predictor:
    def __init__(self):
        """初始化 ResNet-50 模型"""
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.cor_model = None
        self.sag_model = None
        self.cor_checkpoint = None
        self.sag_checkpoint = None
        self.class_labels = None
        self.config = Config()

    def _load_trained_model(self, checkpoint_path, freeze=True):
        # 1. 初始化一个未经预训练的 ResNet-50 骨架
        #    如果你想加载 PyTorch 官方的 ImageNet 预训练权重作为基础，
        #    使用 weights=models.ResNet50_Weights.DEFAULT
        model = models.resnet50(weights=None)

        # 2. 修改全连接层以匹配你保存的模型结构
        num_ftrs = model.fc.in_features
        model.fc = nn.Sequential(
            nn.Linear(num_ftrs, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, self.config.NUM_CLASSES)
        )

        # 3. 加载你的权重
        #    strict=False 可以在某些层不匹配时（比如你这次不想加载FC层）避免报错
        #    但在这里，我们期望结构是完全匹配的，所以默认 strict=True 即可

        print(f"正在从 {checkpoint_path} 加载模型权重...")
        checkpoint = torch.load(checkpoint_path, map_location=self.device, weights_only=True)
        model.load_state_dict(checkpoint['model_state_dict'])

        # 4. 根据需要冻结层
        if freeze:
            # 冻结所有层
            for param in model.parameters():
                param.requires_grad = False
            # 然后只解冻新加的全连接层
            # 注意：这里需要迭代 model.fc 的参数
            for param in model.fc.parameters():
                param.requires_grad = True

        # 5. 将模型移动到指定设备
        model.to(self.device)

        return model, checkpoint

    def load_model(self):
        if self.cor_model is None and self.sag_model is None:
            logger.info("Loading ResNet-50 model...")
            cor_checkpoint_file = os.path.join(self.config.CHECKPOINT_DIR, f"cor_best_mode.pth")
            self.cor_model, self.cor_checkpoint = self._load_trained_model(cor_checkpoint_file)

            sag_checkpoint_file = os.path.join(self.config.CHECKPOINT_DIR, f"sag_best_mode.pth")
            self.sag_model, self.sag_checkpoint = self._load_trained_model(sag_checkpoint_file)
            logger.info(f"Model loaded successfully on {self.device}")


    def _predict_image(self, cor_image_obj, model, checkpoint):
        """
        对单张图像进行预测。
        """
        class_names = checkpoint.get('class_names')  # 使用 .get() 更安全
        if not class_names:
            logger.info("警告: Checkpoint 中未找到 'class_names'。")
        # 图像预处理
        transform = transforms.Compose([
            PadToSquare(self.config.IMAGE_SIZE),
            transforms.Grayscale(num_output_channels=3),
            transforms.ToTensor(),
            transforms.Normalize(mean=self.config.IMAGENET_MEAN, std=self.config.IMAGENET_STD)
        ])

        image = cor_image_obj.convert("RGB")
        image_tensor = transform(image).unsqueeze(0).to(self.device)

        # 预测
        model.eval()
        with torch.no_grad():
            outputs = model(image_tensor)
            # 将模型的输出（logits）转换为概率
            probabilities = torch.nn.functional.softmax(outputs, dim=1)

            # --- 使用 torch.max 一步获取最大概率及其索引 ---
            # torch.max(probabilities, 1) 会返回一个元组 (最大值张量, 索引张量)
            confidence_tensor, predicted_idx_tensor = torch.max(probabilities, 1)
            # 使用 .item() 将它们转换为 Python 数字
            confidence = confidence_tensor.item()
            predicted_idx = predicted_idx_tensor.item()

        # 使用加载的 class_names 映射索引到具体的类别名称
        predicted_class = class_names[predicted_idx]

        # 现在您同样拥有了类别名称和对应的概率
        logger.info(f"预测类别: {predicted_class}")
        logger.info(f"置信度: {confidence:.4f}")
        return predicted_idx, confidence

    def predict(self,cor_img_obj: Image, sag_img_obj:Image):
        self.load_model()
        cor_predicted_class, cor_score = self._predict_image(cor_img_obj, self.cor_model, self.cor_checkpoint)
        sag_predicted_class, sag_score = self._predict_image(sag_img_obj, self.sag_model, self.sag_checkpoint)
        if cor_predicted_class == 0 and sag_predicted_class == 0:
            return 0
        elif cor_predicted_class == 0 or sag_predicted_class == 0:
            print(f'cor:{cor_predicted_class}, sag: {sag_predicted_class} 存在差异，取最大得分情况')
            if cor_score > sag_score:
                return cor_predicted_class
            else:
                return sag_predicted_class
        else:
            return 1

    def predict_score(self,cor_img_obj: Image, sag_img_obj:Image):
        self.load_model()
        cor_predicted_class, cor_score = self._predict_image(cor_img_obj, self.cor_model, self.cor_checkpoint)
        sag_predicted_class, sag_score = self._predict_image(sag_img_obj, self.sag_model, self.sag_checkpoint)
        return {
            "cor": cor_predicted_class,
            "cor_score": cor_score,
            "sag": sag_predicted_class,
            "sag_score": sag_score
        }
predictor = ResNet50Predictor()
