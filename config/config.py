import os
import torch

class Config:
    # 项目基础路径
    BASE_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # 数据集路径
    DATA_DIR = os.path.join(BASE_DIR, "data", 'processed', 'cor_dataset')
    MEDICAL_DATA_DIR = os.path.join(BASE_DIR, "data", 'medical')  # 医学影像数据目录
    TRAIN_DATA_DIR = os.path.join(DATA_DIR, "train")
    VAL_DATA_DIR = os.path.join(DATA_DIR, "val")
    TEST_DATA_DIR = os.path.join(DATA_DIR, "test")

    # 模型和日志保存路径
    CHECKPOINT_DIR = os.path.join(BASE_DIR, "checkpoints")
    LOG_DIR = os.path.join(BASE_DIR, "logs")

    # 实验名称，用于区分不同的实验
    EXP_NAME = "resnet50_baseline_experiment"

    # 如果路径不存在，则自动创建
    os.makedirs(CHECKPOINT_DIR, exist_ok=True)
    os.makedirs(LOG_DIR, exist_ok=True)

    # 训练参数
    NUM_EPOCHS = 25
    BATCH_SIZE = 32
    LEARNING_RATE = 1e-4
    NUM_CLASSES = 4  # MR序列分类：t1, t2, flair, dwi等

    # 医学影像特定参数
    SLICE_MODE = 'middle'  # 'middle' 或 'multi'
    SLICE_AXIS = 'z'       # 当SLICE_MODE='multi'时使用，'x', 'y', 'z'

    # MR序列类别名称
    MR_SEQUENCES = ['t1', 't2', 'flair', 'dwi']

    # 数据集参数
    IMAGE_SIZE = 224
    NUM_WORKERS = 4  # 用于数据加载的子进程数

    # 图像归一化参数，这些是 ImageNet 的均值和标准差，适用于预训练模型
    # 如果您的医学影像数据集特性差异很大，建议重新计算这些值。
    IMAGENET_MEAN = [0.485, 0.456, 0.406]
    IMAGENET_STD = [0.229, 0.224, 0.225]

    LOCAL_TRAIN_MODEL_DIR= os.path.join(BASE_DIR, 'model', 'resnet50.pth')
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")